{"files.encoding": "utf8", "files.associations": {"*.ui": "xml"}, "Lua.runtime.version": "Lua 5.4", "Lua.workspace.library": ["./脚本"], "Lua.workspace.checkThirdParty": false, "Lua.completion.enable": true, "Lua.completion.callSnippet": "Both", "Lua.completion.keywordSnippet": "Both", "Lua.diagnostics.globals": ["require", "findColor", "cmpColor", "getColor", "tap", "longTap", "swipe", "sleep", "toast", "findPic", "findMultiColor", "saveScreenshot", "exitScript", "restartScript", "appIsFront", "appIsRunning", "frontAppName", "getBatteryLevel", "getScreenWidth", "getScreenHeight", "fileExist", "writeFile", "readFile", "httpGet", "httpPost", "encodeBase64", "decodeBase64", "ocr", "console", "ui", "http"], "Lua.hint.enable": true, "Lua.hint.paramType": true, "Lua.hint.setType": true, "Lua.runtime.unicodeName": true}